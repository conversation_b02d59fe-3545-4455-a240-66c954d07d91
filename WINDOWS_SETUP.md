# Windows Setup Guide for Excel Web Application

This guide will help you set up the Excel Web Application on Windows.

## 🎯 Quick Start Options

### Option 1: Docker Setup (Recommended - Easiest)
### Option 2: Local Development Setup
### Option 3: WSL2 + Docker Setup

---

## 🐳 Option 1: Docker Setup (Recommended)

This is the easiest way to get started. Docker will handle all dependencies for you.

### Step 1: Install Docker Desktop

1. **Download Docker Desktop for Windows**
   - Go to: https://www.docker.com/products/docker-desktop/
   - Download "Docker Desktop for Windows"
   - Run the installer

2. **Install Docker Desktop**
   - Run the downloaded installer
   - Follow the installation wizard
   - Restart your computer when prompted

3. **Verify Installation**
   - Open Command Prompt or PowerShell
   - Run: `docker --version`
   - Run: `docker-compose --version`

### Step 2: Start the Application

1. **Open Command Prompt or PowerShell**
   ```cmd
   cd C:\Users\<USER>\Documents\ExcelApp-Augment\APP-003
   ```

2. **Run the setup script**
   ```cmd
   # If you have Git Bash or WSL
   ./setup.sh

   # Or manually with Docker Compose
      docker-compose up --build
   ```

3. **Access the Application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Docs: http://localhost:8000/docs

---

## 💻 Option 2: Local Development Setup

If you prefer to install everything locally without Docker.

### Step 1: Install Python

1. **Download Python**
   - Go to: https://www.python.org/downloads/
   - Download Python 3.11 or later
   - **Important**: Check "Add Python to PATH" during installation

2. **Verify Installation**
   ```cmd
   python --version
   pip --version
   ```

### Step 2: Install Node.js

1. **Download Node.js**
   - Go to: https://nodejs.org/
   - Download the LTS version (18.x or later)
   - Run the installer

2. **Verify Installation**
   ```cmd
   node --version
   npm --version
   ```

### Step 3: Install Redis (Optional but Recommended)

1. **Download Redis for Windows**
   - Go to: https://github.com/microsoftarchive/redis/releases
   - Download the latest .msi file
   - Install Redis

2. **Or use Chocolatey**
   ```cmd
   # Install Chocolatey first: https://chocolatey.org/install
   choco install redis-64
   ```

### Step 4: Setup the Application

1. **Open Command Prompt as Administrator**
   ```cmd
   cd C:\Users\<USER>\Documents\ExcelApp-Augment\APP-003
   ```

2. **Create environment file**
   ```cmd
   copy .env.example .env
   ```

3. **Create directories**
   ```cmd
   mkdir uploads
   mkdir temp
   mkdir logs
   ```

4. **Setup Backend**
   ```cmd
   cd backend
   python -m venv venv
   venv\Scripts\activate
   pip install --upgrade pip
   pip install -r requirements.txt
   cd ..
   ```

5. **Setup Frontend**
   ```cmd
   cd frontend
   npm install
   cd ..
   ```

### Step 5: Start the Application

1. **Start Redis** (if installed)
   ```cmd
   redis-server
   ```

2. **Start Backend** (in one terminal)
   ```cmd
   cd backend
   venv\Scripts\activate
   set PYTHONPATH=%CD%
   uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
   ```

3. **Start Frontend** (in another terminal)
   ```cmd
   cd frontend
   npm start
   ```

---

## 🐧 Option 3: WSL2 + Docker Setup

Use Windows Subsystem for Linux for a Linux-like experience.

### Step 1: Install WSL2

1. **Enable WSL2**
   ```powershell
   # Run in PowerShell as Administrator
   wsl --install
   ```

2. **Restart your computer**

3. **Install Ubuntu**
   ```powershell
   wsl --install -d Ubuntu
   ```

### Step 2: Install Docker in WSL2

1. **Open Ubuntu terminal**
   ```bash
   # Update packages
   sudo apt update

   # Install Docker
   curl -fsSL https://get.docker.com -o get-docker.sh
   sudo sh get-docker.sh

   # Install Docker Compose
   sudo apt install docker-compose

   # Add user to docker group
   sudo usermod -aG docker $USER
   ```

2. **Restart WSL2**
   ```cmd
   wsl --shutdown
   wsl
   ```

### Step 3: Run the Application

1. **Navigate to project**
   ```bash
   cd /mnt/c/Users/<USER>/Documents/ExcelApp-Augment/APP-003
   ```

2. **Start the application**
   ```bash
   ./setup.sh
   ```

---

## 🚀 Quick Start Commands

Once you have the prerequisites installed:

### With Docker:
```cmd
# Navigate to project
cd C:\Users\<USER>\Documents\ExcelApp-Augment\APP-003

# Start application
docker-compose up --build

# Or use the setup script
./setup.sh
```

### Without Docker:
```cmd
# Navigate to project
cd C:\Users\<USER>\Documents\ExcelApp-Augment\APP-003

# Setup (one time)
# Follow Option 2 steps above

# Start Redis
redis-server

# Start Backend (Terminal 1)
cd backend
venv\Scripts\activate
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# Start Frontend (Terminal 2)
cd frontend
npm start
```

---

## 🔧 Troubleshooting

### Common Issues

1. **Port already in use**
   ```cmd
   # Find process using port 8000
   netstat -ano | findstr :8000
   # Kill process
   taskkill /PID <PID> /F
   ```

2. **Python not found**
   - Make sure Python is added to PATH
   - Restart Command Prompt after installation

3. **Docker not starting**
   - Make sure Hyper-V is enabled
   - Restart Docker Desktop
   - Check Docker Desktop settings

4. **Permission denied**
   - Run Command Prompt as Administrator
   - Check file permissions

### Getting Help

- **Docker Issues**: Check Docker Desktop logs
- **Python Issues**: Verify PATH and virtual environment
- **Node.js Issues**: Clear npm cache: `npm cache clean --force`
- **Port Issues**: Use different ports in .env file

---

## 📱 Using the Application

Once running, you can:

1. **Upload Excel Files**
   - Go to http://localhost:3000
   - Drag and drop .xls or .xlsx files
   - Wait for processing to complete

2. **View and Edit**
   - Navigate between sheets using tabs
   - Click cells to edit values
   - View formulas in the formula bar

3. **Manage Images**
   - Upload images via drag and drop
   - Position and resize images
   - Delete unwanted images

4. **API Access**
   - Visit http://localhost:8000/docs for API documentation
   - Use the REST API for programmatic access

---

## 🎉 Next Steps

After setup:

1. **Test with sample files**: Upload some Excel files to test functionality
2. **Explore the API**: Visit http://localhost:8000/docs
3. **Check logs**: Monitor application logs for any issues
4. **Customize**: Modify .env file for your specific needs

---

## 📞 Support

If you encounter issues:

1. Check the troubleshooting section above
2. Review application logs
3. Ensure all prerequisites are properly installed
4. Try restarting the services

The application includes comprehensive error handling and logging to help diagnose issues.
