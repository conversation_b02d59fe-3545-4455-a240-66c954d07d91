#!/bin/bash

# Excel Web Application Local Setup Script
# This script sets up the application for local development without Docker

set -e  # Exit on any error

echo "🚀 Setting up Excel Web Application (Local Development)..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is not installed. Please install Python 3.8+ first."
        exit 1
    fi
    
    python_version=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1-2)
    print_success "Python $python_version found"
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 16+ first."
        exit 1
    fi
    
    node_version=$(node --version)
    print_success "Node.js $node_version found"
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm first."
        exit 1
    fi
    
    npm_version=$(npm --version)
    print_success "npm $npm_version found"
}

# Create directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p uploads
    mkdir -p temp
    mkdir -p logs
    
    print_success "Directories created"
}

# Setup environment
setup_environment() {
    print_status "Setting up environment configuration..."
    
    if [ ! -f .env ]; then
        cp .env.example .env
        print_success "Environment file created from template"
        
        # Update .env for local development
        sed -i 's/REDIS_HOST=redis/REDIS_HOST=localhost/' .env 2>/dev/null || \
        sed -i '' 's/REDIS_HOST=redis/REDIS_HOST=localhost/' .env 2>/dev/null || true
        
        print_warning "Please review and update the .env file with your specific configuration"
    else
        print_warning ".env file already exists, skipping creation"
    fi
}

# Setup backend
setup_backend() {
    print_status "Setting up backend..."
    
    cd backend
    
    # Create virtual environment
    if [ ! -d "venv" ]; then
        print_status "Creating Python virtual environment..."
        python3 -m venv venv
        print_success "Virtual environment created"
    fi
    
    # Activate virtual environment
    source venv/bin/activate || source venv/Scripts/activate
    
    # Install dependencies
    print_status "Installing Python dependencies..."
    pip install --upgrade pip
    pip install -r requirements.txt
    
    print_success "Backend dependencies installed"
    
    cd ..
}

# Setup frontend
setup_frontend() {
    print_status "Setting up frontend..."
    
    cd frontend
    
    # Install dependencies
    print_status "Installing Node.js dependencies..."
    npm install
    
    print_success "Frontend dependencies installed"
    
    cd ..
}

# Install Redis (optional)
install_redis() {
    print_status "Checking Redis installation..."
    
    if ! command -v redis-server &> /dev/null; then
        print_warning "Redis is not installed. The application will work without Redis but with reduced performance."
        print_status "To install Redis:"
        echo "  - Ubuntu/Debian: sudo apt install redis-server"
        echo "  - macOS: brew install redis"
        echo "  - Windows: Download from https://redis.io/download"
    else
        print_success "Redis is available"
    fi
}

# Create start scripts
create_start_scripts() {
    print_status "Creating start scripts..."
    
    # Backend start script
    cat > start-backend.sh << 'EOF'
#!/bin/bash
echo "Starting backend..."
cd backend
source venv/bin/activate || source venv/Scripts/activate
export PYTHONPATH=$PWD
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
EOF
    chmod +x start-backend.sh
    
    # Frontend start script
    cat > start-frontend.sh << 'EOF'
#!/bin/bash
echo "Starting frontend..."
cd frontend
npm start
EOF
    chmod +x start-frontend.sh
    
    # Combined start script
    cat > start-app.sh << 'EOF'
#!/bin/bash
echo "Starting Excel Web Application..."

# Start Redis if available
if command -v redis-server &> /dev/null; then
    echo "Starting Redis..."
    redis-server --daemonize yes --port 6379
fi

# Start backend in background
echo "Starting backend..."
./start-backend.sh &
BACKEND_PID=$!

# Wait a moment for backend to start
sleep 5

# Start frontend
echo "Starting frontend..."
./start-frontend.sh &
FRONTEND_PID=$!

echo ""
echo "🎉 Excel Web Application is starting!"
echo ""
echo "📱 Frontend: http://localhost:3000"
echo "🔧 Backend API: http://localhost:8000"
echo "📚 API Documentation: http://localhost:8000/docs"
echo ""
echo "Press Ctrl+C to stop all services"

# Wait for user interrupt
trap 'echo "Stopping services..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit' INT
wait
EOF
    chmod +x start-app.sh
    
    print_success "Start scripts created"
}

# Main execution
main() {
    echo "=================================================="
    echo "    Excel Web Application Local Setup"
    echo "=================================================="
    echo ""
    
    check_prerequisites
    create_directories
    setup_environment
    setup_backend
    setup_frontend
    install_redis
    create_start_scripts
    
    echo ""
    print_success "🎉 Setup completed successfully!"
    echo ""
    echo "To start the application:"
    echo "  ./start-app.sh"
    echo ""
    echo "Or start services individually:"
    echo "  ./start-backend.sh  (in one terminal)"
    echo "  ./start-frontend.sh (in another terminal)"
    echo ""
    echo "Note: Make sure Redis is running for optimal performance"
    echo "      sudo systemctl start redis (Linux)"
    echo "      brew services start redis (macOS)"
    echo ""
}

main
