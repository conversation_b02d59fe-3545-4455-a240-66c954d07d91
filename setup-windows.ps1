# Excel Web Application Windows Setup Script
# Run this script in PowerShell as Administrator

param(
    [switch]$InstallPrerequisites,
    [switch]$SetupApp,
    [switch]$StartApp
)

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Cyan"

function Write-Status {
    param($Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Blue
}

function Write-Success {
    param($Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Green
}

function Write-Warning {
    param($Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Yellow
}

function Write-Error {
    param($Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Red
}

function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Install-Prerequisites {
    Write-Status "Installing prerequisites..."
    
    # Check if running as administrator
    if (-not (Test-Administrator)) {
        Write-Error "Please run this script as Administrator to install prerequisites"
        exit 1
    }
    
    # Install Chocolatey if not present
    if (-not (Get-Command choco -ErrorAction SilentlyContinue)) {
        Write-Status "Installing Chocolatey..."
        Set-ExecutionPolicy Bypass -Scope Process -Force
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
        iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
        Write-Success "Chocolatey installed"
    }
    
    # Install Python
    if (-not (Get-Command python -ErrorAction SilentlyContinue)) {
        Write-Status "Installing Python..."
        choco install python -y
        Write-Success "Python installed"
    } else {
        Write-Success "Python already installed"
    }
    
    # Install Node.js
    if (-not (Get-Command node -ErrorAction SilentlyContinue)) {
        Write-Status "Installing Node.js..."
        choco install nodejs -y
        Write-Success "Node.js installed"
    } else {
        Write-Success "Node.js already installed"
    }
    
    # Install Git
    if (-not (Get-Command git -ErrorAction SilentlyContinue)) {
        Write-Status "Installing Git..."
        choco install git -y
        Write-Success "Git installed"
    } else {
        Write-Success "Git already installed"
    }
    
    # Install Redis
    if (-not (Get-Command redis-server -ErrorAction SilentlyContinue)) {
        Write-Status "Installing Redis..."
        choco install redis-64 -y
        Write-Success "Redis installed"
    } else {
        Write-Success "Redis already installed"
    }
    
    Write-Warning "Please restart your PowerShell session to refresh PATH variables"
}

function Setup-Application {
    Write-Status "Setting up Excel Web Application..."
    
    # Check prerequisites
    $pythonExists = Get-Command python -ErrorAction SilentlyContinue
    $nodeExists = Get-Command node -ErrorAction SilentlyContinue
    
    if (-not $pythonExists) {
        Write-Error "Python not found. Please install Python first or run with -InstallPrerequisites"
        exit 1
    }
    
    if (-not $nodeExists) {
        Write-Error "Node.js not found. Please install Node.js first or run with -InstallPrerequisites"
        exit 1
    }
    
    # Create directories
    Write-Status "Creating directories..."
    New-Item -ItemType Directory -Force -Path "uploads" | Out-Null
    New-Item -ItemType Directory -Force -Path "temp" | Out-Null
    New-Item -ItemType Directory -Force -Path "logs" | Out-Null
    Write-Success "Directories created"
    
    # Setup environment file
    if (-not (Test-Path ".env")) {
        Write-Status "Creating environment file..."
        Copy-Item ".env.example" ".env"
        
        # Update for Windows
        (Get-Content ".env") -replace "REDIS_HOST=redis", "REDIS_HOST=localhost" | Set-Content ".env"
        Write-Success "Environment file created"
    }
    
    # Setup backend
    Write-Status "Setting up backend..."
    Set-Location "backend"
    
    if (-not (Test-Path "venv")) {
        Write-Status "Creating Python virtual environment..."
        python -m venv venv
        Write-Success "Virtual environment created"
    }
    
    Write-Status "Activating virtual environment and installing dependencies..."
    & "venv\Scripts\Activate.ps1"
    python -m pip install --upgrade pip
    pip install -r requirements.txt
    Write-Success "Backend dependencies installed"
    
    Set-Location ".."
    
    # Setup frontend
    Write-Status "Setting up frontend..."
    Set-Location "frontend"
    
    Write-Status "Installing Node.js dependencies..."
    npm install
    Write-Success "Frontend dependencies installed"
    
    Set-Location ".."
    
    Write-Success "Application setup completed!"
}

function Start-Application {
    Write-Status "Starting Excel Web Application..."
    
    # Start Redis if available
    $redisExists = Get-Command redis-server -ErrorAction SilentlyContinue
    if ($redisExists) {
        Write-Status "Starting Redis..."
        Start-Process "redis-server" -WindowStyle Hidden
        Write-Success "Redis started"
    } else {
        Write-Warning "Redis not found. Application will work with reduced performance."
    }
    
    # Start backend
    Write-Status "Starting backend..."
    $backendJob = Start-Job -ScriptBlock {
        Set-Location $using:PWD
        Set-Location "backend"
        & "venv\Scripts\Activate.ps1"
        $env:PYTHONPATH = Get-Location
        uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
    }
    
    # Wait a moment for backend to start
    Start-Sleep -Seconds 5
    
    # Start frontend
    Write-Status "Starting frontend..."
    $frontendJob = Start-Job -ScriptBlock {
        Set-Location $using:PWD
        Set-Location "frontend"
        npm start
    }
    
    Write-Success "Excel Web Application is starting!"
    Write-Host ""
    Write-Host "📱 Frontend: http://localhost:3000" -ForegroundColor Green
    Write-Host "🔧 Backend API: http://localhost:8000" -ForegroundColor Green
    Write-Host "📚 API Documentation: http://localhost:8000/docs" -ForegroundColor Green
    Write-Host ""
    Write-Host "Press Ctrl+C to stop all services" -ForegroundColor Yellow
    
    # Wait for jobs and handle cleanup
    try {
        Wait-Job $backendJob, $frontendJob
    }
    finally {
        Write-Status "Stopping services..."
        Stop-Job $backendJob, $frontendJob -ErrorAction SilentlyContinue
        Remove-Job $backendJob, $frontendJob -ErrorAction SilentlyContinue
        
        # Stop Redis
        Get-Process redis-server -ErrorAction SilentlyContinue | Stop-Process -ErrorAction SilentlyContinue
    }
}

function Show-Help {
    Write-Host "Excel Web Application Windows Setup Script" -ForegroundColor Green
    Write-Host ""
    Write-Host "Usage:" -ForegroundColor Yellow
    Write-Host "  .\setup-windows.ps1 -InstallPrerequisites    # Install Python, Node.js, Redis"
    Write-Host "  .\setup-windows.ps1 -SetupApp                # Setup the application"
    Write-Host "  .\setup-windows.ps1 -StartApp                # Start the application"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  # Full setup (run as Administrator)"
    Write-Host "  .\setup-windows.ps1 -InstallPrerequisites"
    Write-Host "  .\setup-windows.ps1 -SetupApp"
    Write-Host "  .\setup-windows.ps1 -StartApp"
    Write-Host ""
    Write-Host "  # Quick start (if prerequisites already installed)"
    Write-Host "  .\setup-windows.ps1 -SetupApp -StartApp"
    Write-Host ""
}

# Main execution
if ($InstallPrerequisites) {
    Install-Prerequisites
}
elseif ($SetupApp) {
    Setup-Application
    if ($StartApp) {
        Start-Application
    }
}
elseif ($StartApp) {
    Start-Application
}
else {
    Show-Help
}
