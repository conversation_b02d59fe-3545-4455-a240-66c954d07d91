@echo off
echo ========================================
echo   Excel Web Application - Windows
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Python is not installed or not in PATH
    echo.
    echo Please install Python first:
    echo 1. Go to https://www.python.org/downloads/
    echo 2. Download Python 3.11 or later
    echo 3. Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed or not in PATH
    echo.
    echo Please install Node.js first:
    echo 1. Go to https://nodejs.org/
    echo 2. Download the LTS version
    echo 3. Run the installer
    echo.
    pause
    exit /b 1
)

echo [INFO] Prerequisites check passed!
echo.

REM Create directories if they don't exist
if not exist "uploads" mkdir uploads
if not exist "temp" mkdir temp
if not exist "logs" mkdir logs

REM Create .env file if it doesn't exist
if not exist ".env" (
    echo [INFO] Creating environment file...
    copy ".env.example" ".env" >nul
    echo [SUCCESS] Environment file created
)

REM Setup backend if not already done
if not exist "backend\venv" (
    echo [INFO] Setting up backend for the first time...
    cd backend
    python -m venv venv
    call venv\Scripts\activate.bat
    python -m pip install --upgrade pip
    pip install -r requirements.txt
    cd ..
    echo [SUCCESS] Backend setup completed
)

REM Setup frontend if not already done
if not exist "frontend\node_modules" (
    echo [INFO] Setting up frontend for the first time...
    cd frontend
    npm install
    cd ..
    echo [SUCCESS] Frontend setup completed
)

echo.
echo [INFO] Starting Excel Web Application...
echo.

REM Start Redis if available
redis-server --version >nul 2>&1
if %errorlevel% equ 0 (
    echo [INFO] Starting Redis...
    start /B redis-server
    timeout /t 2 >nul
)

REM Start backend
echo [INFO] Starting backend...
start "Backend" cmd /k "cd backend && venv\Scripts\activate.bat && set PYTHONPATH=%cd% && uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload"

REM Wait a moment for backend to start
timeout /t 5 >nul

REM Start frontend
echo [INFO] Starting frontend...
start "Frontend" cmd /k "cd frontend && npm start"

echo.
echo ========================================
echo   Excel Web Application Started!
echo ========================================
echo.
echo Frontend:     http://localhost:3000
echo Backend API:  http://localhost:8000
echo API Docs:     http://localhost:8000/docs
echo.
echo Two new command windows have opened:
echo - Backend (Python/FastAPI)
echo - Frontend (React)
echo.
echo Close those windows to stop the application.
echo.
pause
